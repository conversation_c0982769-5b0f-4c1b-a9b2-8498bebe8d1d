import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_portrait_data.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';

/// 分类导航栏状态管理


/// 页面专用的分类导航状态管理，支持初始索引
final pageSpecificCategoryNavigationProvider = StateNotifierProvider.family
    .autoDispose<CategoryNavigationNotifier, CategoryNavigationState, int>(
        (ref, initialIndex) {
  return CategoryNavigationNotifier(initialIndex: initialIndex);
});

class CategoryNavigationState {
  final int selectedIndex;
  final bool isExpanded;

  CategoryNavigationState({
    this.selectedIndex = 0,
    this.isExpanded = true,
  });

  CategoryNavigationState copyWith({
    int? selectedIndex,
    bool? isExpanded,
  }) {
    return CategoryNavigationState(
      selectedIndex: selectedIndex ?? this.selectedIndex,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }
}

class CategoryNavigationNotifier
    extends StateNotifier<CategoryNavigationState> {
  CategoryNavigationNotifier({int initialIndex = 0})
      : super(CategoryNavigationState(selectedIndex: initialIndex));

  void selectCategory(int index) {
    state = state.copyWith(selectedIndex: index);
  }

  void toggleExpansion() {
    state = state.copyWith(isExpanded: !state.isExpanded);
  }

  void initializeWithIndex(int index) {
    state = CategoryNavigationState(
      selectedIndex: index,
      isExpanded: state.isExpanded,
    );
  }

  /// 立即设置索引，不触发重建
  void setIndexImmediately(int index) {
    if (state.selectedIndex != index) {
      state = state.copyWith(selectedIndex: index);
    }
  }
}

class PhotoPortraitCategoryPage extends ConsumerStatefulWidget {
  const PhotoPortraitCategoryPage({
    super.key,
    this.initialCategoryIndex = 0,
    this.categories,
  });

  /// 初始选中的分类索引
  final int initialCategoryIndex;
  final List<PhotoPortraitCategory>? categories;

  @override
  ConsumerState<PhotoPortraitCategoryPage> createState() => _PhotoPortraitCategoryPageState();
}

class _PhotoPortraitCategoryPageState
    extends ConsumerState<PhotoPortraitCategoryPage> {
  // 模拟数据 - 写真分类数据（与主页面保持一致）
  List<PhotoPortraitCategory> get mockCategoryData => widget.categories ?? PhotoPortraitData.mockCategoryData;

  @override
  Widget build(BuildContext context) {
    // 使用页面专用的 provider，直接传入初始索引，完全避免延迟
    final navigationState = ref.watch(pageSpecificCategoryNavigationProvider(widget.initialCategoryIndex));
    final categories = mockCategoryData;

    final selectedCategory = categories.isNotEmpty && navigationState.selectedIndex < categories.length
        ? categories[navigationState.selectedIndex]
        : null;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          selectedCategory?.caseName ?? "写真分类",
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () => {
              ref
                  .read(pageSpecificCategoryNavigationProvider(widget.initialCategoryIndex).notifier)
                  .toggleExpansion()
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          // 分类导航栏
          _buildCategoryNavigation(context, ref, categories, navigationState),
          // 分类内容
          Expanded(
            child: selectedCategory != null
                ? _buildCategoryContent(context, selectedCategory)
                : const Center(
                    child: Text(
                      "暂无分类数据",
                      style: TextStyle(color: Colors.white54),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建分类导航栏
  Widget _buildCategoryNavigation(
    BuildContext context,
    WidgetRef ref,
    List<PhotoPortraitCategory> categories,
    CategoryNavigationState navigationState,
  ) {
    // 分类导航栏重建
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: navigationState.isExpanded ? 60 : 40,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        separatorBuilder: (context, index) => const SizedBox(width: 16),
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = navigationState.selectedIndex == index;

          return _buildCategoryTab(
            context,
            ref,
            category,
            index,
            isSelected,
            navigationState.isExpanded,
            widget.initialCategoryIndex,
          );
        },
      ),
    );
  }

  /// 构建单个分类标签
  Widget _buildCategoryTab(
    BuildContext context,
    WidgetRef ref,
    PhotoPortraitCategory category,
    int index,
    bool isSelected,
    bool isExpanded,
    int initialCategoryIndex,
  ) {
    return InkWell(
      onTap: () {
        ref
            .read(pageSpecificCategoryNavigationProvider(initialCategoryIndex).notifier)
            .selectCategory(index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: isExpanded ? 16 : 12,
          vertical: isExpanded ? 8 : 6,
        ),
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFF4A90E2), Color(0xFF357ABD)],
                )
              : null,
          color: isSelected ? null : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? Colors.transparent
                : Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            category.caseName ?? "",
            style: TextStyle(
              fontSize: isExpanded ? 14 : 12,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建分类内容
  Widget _buildCategoryContent(
      BuildContext context, PhotoPortraitCategory category) {
    final details = category.details ?? [];

    if (details.isEmpty) {
      return const Center(
        child: Text(
          "该分类暂无内容",
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: details.length,
      itemBuilder: (context, index) {
        final detail = details[index];
        return _buildCategoryDetailItem(context, detail);
      },
    );
  }

  /// 构建分类详情项
  Widget _buildCategoryDetailItem(
      BuildContext context, PhotoPortraitCategoryDetail detail) {
    return InkWell(
      onTap: () => _handleDetailItemTap(detail),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // 网络图片
              CachedNetworkImage(
                imageUrl: detail.caseImage ?? "",
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.purple.withValues(alpha: 0.6),
                        Colors.pink.withValues(alpha: 0.8),
                        Colors.orange.withValues(alpha: 0.6),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.grey.withValues(alpha: 0.6),
                        Colors.grey.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                      size: 32,
                    ),
                  ),
                ),
              ),
              // 渐变遮罩
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.6),
                    ],
                  ),
                ),
              ),
              // 标题和描述
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (detail.caseTitle?.isNotEmpty == true)
                        Text(
                          detail.caseTitle!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      if (detail.casePrompt?.isNotEmpty == true) ...[
                        const SizedBox(height: 4),
                        Text(
                          detail.casePrompt!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white70,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理详情项点击
  void _handleDetailItemTap(PhotoPortraitCategoryDetail detail) {
    // TODO: 跳转到写真生成页面
    debugPrint("选择写真: ${detail.caseTitle}，ID: ${detail.caseId}");
  }
}
